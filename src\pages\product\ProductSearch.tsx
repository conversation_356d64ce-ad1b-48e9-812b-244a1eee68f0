// components/ProductSearch.tsx
import React, { useState, useCallback, useEffect } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box } from "@mui/material";
// import { useNavigate } from "react-router-dom"; // Removed for now
import FilterRow from "../../components/FilterRow";
import { debounce } from "@mui/material/utils";
import { useProduct } from "../../services/hooks/product.hooks";

// 📝 LEARNING NOTE: Product interface is now imported from the API service

// 📝 LEARNING NOTE: Mock data removed - now using live API integration

const ProductSearch = () => {
  // 📝 LEARNING NOTE: Using product hook for API integration
  const {
    listItems,
    products,
    searchLoading,
    totalCount,
    getListItems,
    searchProductsWithParams,
    clearSearch,
  } = useProduct();

  const [genericSearch, setGenericSearch] = useState("");
  
  // 📝 LEARNING NOTE: Filter state matching API parameters
  const [filters, setFilters] = useState({
    PRDNO: "",
    PRMSDescription: "",
    RetailUPC: "",
    BrandName: "",
    SkuName: "",
    AssociatedItem: ""
  });

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  const [isFilterApplied, setIsFilterApplied] = useState(false);
  const [filterTrigger, setFilterTrigger] = useState(0);

  // 📝 LEARNING NOTE: Reset filters when component mounts and fetch list items
  useEffect(() => {
    setFilters({
      PRDNO: "",
      PRMSDescription: "",
      RetailUPC: "",
      BrandName: "",
      SkuName: "",
      AssociatedItem: ""
    });
    setGenericSearch("");
    getListItems(); // Fetch dropdown options
  }, [getListItems]);

  // 📝 LEARNING NOTE: API call for product search
  const fetchProducts = useCallback(() => {
    // Allow search if either search bar has content OR filters are applied
    if (!genericSearch && !isFilterApplied) return;

    const searchParams = {
      page: pagination.page,
      limit: pagination.rowsPerPage,
      search_query: genericSearch || undefined,
      PRDNO: filters.PRDNO || undefined,
      PRMSDescription: filters.PRMSDescription || undefined,
      RetailUPC: filters.RetailUPC || undefined,
      BrandName: filters.BrandName || undefined,
      SkuName: filters.SkuName || undefined,
      AssociatedItem: filters.AssociatedItem || undefined,
    };

    searchProductsWithParams(searchParams);
  }, [
    genericSearch,
    pagination.page,
    pagination.rowsPerPage,
    isFilterApplied,
    filters,
    searchProductsWithParams
  ]);

  // 📝 LEARNING NOTE: Debounced search handler (2 seconds delay for performance)
  const handleSearch = useCallback(
    debounce((query: string) => {
      setGenericSearch(query);
      setPagination((prev) => ({ ...prev, page: 1 }));
      // Note: We don't set isFilterApplied here - search bar works independently
    }, 2000),
    [],
  );

  // 📝 LEARNING NOTE: Filter change handler
  const handleFilterChange = (name: string, value: string) => {
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // 📝 LEARNING NOTE: Apply filters handler
  const handleApplyFilters = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(true);
    // Increment trigger to force re-fetch even when isFilterApplied is already true
    setFilterTrigger(prev => prev + 1);
  };

  // 📝 LEARNING NOTE: Reset filters handler
  const handleResetFilters = () => {
    setFilters({
      PRDNO: "",
      PRMSDescription: "",
      RetailUPC: "",
      BrandName: "",
      SkuName: "",
      AssociatedItem: ""
    });
    setGenericSearch("");
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(false);
    setFilterTrigger(0);
    clearSearch(); // Clear search results from Redux store
  };

  // 📝 LEARNING NOTE: Product click handler (for future navigation)
  const handleProductClick = (prdno: string, descp: string) => {
    // TODO: Navigate to product details page
    console.log(`Navigate to product: ${prdno} - ${descp}`);
    // navigate(`/product-details/${prdno}`, { state: { descp } });
  };

  // 📝 LEARNING NOTE: Pagination handler
  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  // 📝 LEARNING NOTE: Fetch products when search, pagination, or filter trigger changes
  useEffect(() => {
    // Search bar: Auto-search with debouncing (4 seconds)
    if (genericSearch) {
      fetchProducts();
      return;
    }

    // Filter fields: Only search when Apply button is clicked (isFilterApplied = true)
    if (isFilterApplied) {
      fetchProducts();
    }
  }, [genericSearch, pagination.page, pagination.rowsPerPage, isFilterApplied, filterTrigger, fetchProducts]);

  // 📝 LEARNING NOTE: Table columns matching your API response structure
  const columns = [
    {
      id: "PRDNO",
      label: "Product No",
      description: "Product number identifier",
      format: (value: string, row: any) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => handleProductClick(value, row.DESCP)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "DESCP",
      label: "Description",
      description: "Product description",
    },
    {
      id: "ItemID",
      label: "Item ID",
      description: "Internal item identifier",
    },
    {
      id: "SkuName",
      label: "SKU Name",
      description: "Stock keeping unit name",
    },
    {
      id: "AssociatedItem",
      label: "Associated Item",
      description: "Related or associated product item",
    },
    {
      id: "BrandName",
      label: "Brand",
      description: "Product brand name",
    },
    {
      id: "REUPC",
      label: "Retail UPC",
      description: "Retail Universal Product Code",
    },
    {
      id: "GRUPC",
      label: "Group UPC",
      description: "Group Universal Product Code",
    },
  ];

  // 📝 LEARNING NOTE: Filter configurations matching API parameters
  const filterConfigs = [
    {
      name: "PRDNO",
      label: "PRDNO",
      type: "text" as const,
      value: filters.PRDNO,
      onChange: (value: string) => handleFilterChange("PRDNO", value),
    },
    {
      name: "PRMSDescription",
      label: "Description",
      type: "text" as const,
      value: filters.PRMSDescription,
      onChange: (value: string) => handleFilterChange("PRMSDescription", value),
    },
    {
      name: "RetailUPC",
      label: "Retail UPC",
      type: "text" as const,
      value: filters.RetailUPC,
      onChange: (value: string) => handleFilterChange("RetailUPC", value),
    },
    {
      name: "BrandName",
      label: "Brand",
      type: "select" as const,
      value: filters.BrandName,
      onChange: (value: string) => handleFilterChange("BrandName", value),
      options: [
        { value: "", label: "" }, // Empty option for unselecting
        ...listItems.map(item => ({ value: item, label: item }))
      ],
    },
    {
      name: "SkuName",
      label: "SKU Name",
      type: "text" as const,
      value: filters.SkuName,
      onChange: (value: string) => handleFilterChange("SkuName", value),
    },
    {
      name: "AssociatedItem",
      label: "Associated Item",
      type: "text" as const,
      value: filters.AssociatedItem,
      onChange: (value: string) => handleFilterChange("AssociatedItem", value),
    },
  ];

  return (
    <div>
      {/* 📝 LEARNING NOTE: TopBarLayout with breadcrumb and search - same as Store Search */}
      <TopBarLayout 
        breadcrumbItems={["Product", "Product Search"]} 
        onSearchChange={handleSearch} 
      />

      {/* 📝 LEARNING NOTE: FilterRow with your specified fields */}
      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {/* 📝 LEARNING NOTE: Table component - show when search bar has content OR filters are applied */}
      {(genericSearch || isFilterApplied) && (
        <TableComponent
          columns={columns}
          rows={products}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={totalCount}
          onPageChange={handlePageChange}
          showActions={false}
          isLoading={searchLoading}
        />
      )}
    </div>
  );
};

export default ProductSearch;
